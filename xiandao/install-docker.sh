#!/bin/bash

# Docker and Docker Compose Installation Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "Cannot detect OS version"
        exit 1
    fi
    log_info "Detected OS: $OS $VER"
}

# Install Docker
install_docker() {
    log_info "Installing Docker..."
    
    # Update package index
    apt-get update
    
    # Install required packages
    apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Update package index again
    apt-get update
    
    # Install Docker
    apt-get install -y docker-ce docker-ce-cli containerd.io
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    log_success "Docker installed successfully"
}

# Install Docker Compose
install_docker_compose() {
    log_info "Installing Docker Compose..."
    
    # Get latest version
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    # Download Docker Compose
    curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # Make it executable
    chmod +x /usr/local/bin/docker-compose
    
    # Create symlink
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose installed successfully"
}

# Add user to docker group
setup_user_permissions() {
    log_info "Setting up user permissions..."
    
    # Get the original user (not root)
    ORIGINAL_USER=${SUDO_USER:-$USER}
    
    if [ "$ORIGINAL_USER" != "root" ]; then
        usermod -aG docker $ORIGINAL_USER
        log_success "Added $ORIGINAL_USER to docker group"
        log_warning "Please log out and log back in for group changes to take effect"
    fi
}

# Test installation
test_installation() {
    log_info "Testing installation..."
    
    # Test Docker
    if docker --version > /dev/null 2>&1; then
        log_success "Docker is working: $(docker --version)"
    else
        log_error "Docker installation failed"
        return 1
    fi
    
    # Test Docker Compose
    if docker-compose --version > /dev/null 2>&1; then
        log_success "Docker Compose is working: $(docker-compose --version)"
    else
        log_error "Docker Compose installation failed"
        return 1
    fi
    
    # Test Docker daemon
    if docker info > /dev/null 2>&1; then
        log_success "Docker daemon is running"
    else
        log_error "Docker daemon is not running"
        return 1
    fi
}

# Main installation process
main() {
    echo "=========================================="
    echo "Docker & Docker Compose Installation"
    echo "=========================================="
    
    check_root
    detect_os
    
    # Check if Docker is already installed
    if command -v docker > /dev/null 2>&1; then
        log_warning "Docker is already installed: $(docker --version)"
    else
        install_docker
    fi
    
    # Check if Docker Compose is already installed
    if command -v docker-compose > /dev/null 2>&1; then
        log_warning "Docker Compose is already installed: $(docker-compose --version)"
    else
        install_docker_compose
    fi
    
    setup_user_permissions
    test_installation
    
    echo
    echo "=========================================="
    echo "Installation Complete!"
    echo "=========================================="
    echo
    echo "Next steps:"
    echo "1. Log out and log back in (or restart)"
    echo "2. Test with: docker run hello-world"
    echo "3. Deploy Xiandao: ./deploy.sh"
    echo
    log_success "Docker and Docker Compose installed successfully!"
}

# Run main function
main "$@"
