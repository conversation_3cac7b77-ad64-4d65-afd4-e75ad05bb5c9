# Dockerfile for Pike backend server
FROM ubuntu:20.04

# Avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    wget \
    curl \
    git \
    libmysqlclient-dev \
    mysql-client \
    nettle-dev \
    libgmp-dev \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libffi-dev \
    liblzma-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Create game directory
RUN mkdir -p /usr/local/games/xiand

# Download and compile Pike 8.0
WORKDIR /tmp
RUN wget http://pike.lysator.liu.se/pub/pike/all/8.0.1738/Pike-v8.0.1738.tar.gz \
    && tar -xzf Pike-v8.0.1738.tar.gz \
    && cd Pike-v8.0.1738 \
    && make configure \
    && ./configure --prefix=/usr/local --with-mysql \
    && make \
    && make install \
    && ln -sf /usr/local/bin/pike /usr/local/bin/pike8

# Copy game files
COPY . /usr/local/games/xiand/
WORKDIR /usr/local/games/xiand

# Run the copy script to set up game files
RUN cd not_in_svn && chmod +x cp_file.sh && ./cp_file.sh

# Create necessary directories
RUN mkdir -p /usr/local/games/xiand/log \
    && mkdir -p /usr/local/games/xiand/data_xiand \
    && chmod +x startup.sh

# Update configuration files for Docker environment
RUN sed -i 's/127\.0\.0\.1/0.0.0.0/g' lowlib/system/include/globals.h \
    && sed -i 's/127\.0\.0\.1/0.0.0.0/g' startup.sh

# Expose the game port
EXPOSE 13800

# Start the Pike server
CMD ["./startup.sh"]
