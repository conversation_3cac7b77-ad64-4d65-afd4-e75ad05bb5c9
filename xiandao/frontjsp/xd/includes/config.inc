<%!
public String ip="pike-backend";
public int port=13800;
public String webpath="xd";
public String projname="gamelib";
public String gamename="xd";
public String game_pre="xd01";
public String area="xd01";
public String gamename_cn="天下仙道网游";
public String index_url="http://localhost:8080/xd/pc.jsp";
public String internet_addr="http://localhost:8080";
public String internet_ip="localhost";
public String our_ip = "localhost";
public int game_close = 0;        //游戏是否对外关闭的标志位，设置该标示位为1时，只有ip为"our_ip"的用户可以访问
%>
