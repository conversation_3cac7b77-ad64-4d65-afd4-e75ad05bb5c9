# Docker and Docker Compose Installation Guide

## Quick Installation (Ubuntu/Debian)

### Install Docker
```bash
# Update package index
sudo apt update

# Install required packages
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package index again
sudo apt update

# Install Docker
sudo apt install -y docker-ce docker-ce-cli containerd.io

# Add your user to docker group (to run without sudo)
sudo usermod -aG docker $USER

# Start and enable Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### Install Docker Compose
```bash
# Download Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Make it executable
sudo chmod +x /usr/local/bin/docker-compose

# Create symlink (optional)
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# Verify installation
docker-compose --version
```

### Alternative: Install via pip
```bash
# Install pip if not available
sudo apt install -y python3-pip

# Install docker-compose via pip
pip3 install docker-compose

# Add to PATH if needed
echo 'export PATH=$PATH:~/.local/bin' >> ~/.bashrc
source ~/.bashrc
```

### Alternative: Install via snap
```bash
sudo snap install docker
sudo snap install docker-compose
```

## Post-Installation Steps

1. **Log out and log back in** (or restart) for group changes to take effect
2. **Test Docker installation:**
   ```bash
   docker --version
   docker run hello-world
   ```
3. **Test Docker Compose:**
   ```bash
   docker-compose --version
   ```

## If You Can't Install Docker

### Option 1: Use Individual Docker Commands
I can create a script that runs individual `docker run` commands instead of using docker-compose.

### Option 2: Native Installation
Install Pike, MySQL, and Tomcat directly on your system without Docker.

### Option 3: Use Podman (Docker alternative)
```bash
sudo apt install -y podman podman-compose
alias docker=podman
alias docker-compose=podman-compose
```

## Troubleshooting

### Permission Denied
If you get permission errors:
```bash
sudo usermod -aG docker $USER
newgrp docker
```

### Docker Daemon Not Running
```bash
sudo systemctl start docker
sudo systemctl status docker
```

### Docker Compose Command Not Found
```bash
# Check if it's installed
which docker-compose

# If not found, try alternative installation methods above
```

## Quick Test
After installation, test with:
```bash
cd /home/<USER>/Documents/augment-projects/xiandao
./deploy.sh
```

Would you like me to create alternative deployment scripts that don't require Docker Compose?
