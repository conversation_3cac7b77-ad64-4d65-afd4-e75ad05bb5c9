# Xiandao Docker Setup - Testing and Validation Guide

This document provides comprehensive testing procedures to validate the Xiandao Docker setup.

## Pre-Testing Checklist

### System Requirements
- [ ] Docker installed (version 20.10+)
- [ ] Docker Compose installed (version 1.29+)
- [ ] At least 2GB available RAM
- [ ] Ports 3306, 8080, and 13800 available

### File Verification
- [ ] All Dockerfiles present (`Dockerfile.pike`, `Dockerfile.tomcat`)
- [ ] `docker-compose.yml` configured
- [ ] SQL initialization script in `sql/01-init.sql`
- [ ] Configuration files updated for Docker networking

## Testing Procedure

### Step 1: Build and Start Services

```bash
# Navigate to project directory
cd xiandao

# Build and start all services
docker-compose up --build -d

# Check service status
docker-compose ps
```

**Expected Output:**
```
Name                Command               State                    Ports
xiandao-mysql     docker-entrypoint.sh mysqld   Up      0.0.0.0:3306->3306/tcp
xiandao-pike      ./startup.sh                   Up      0.0.0.0:13800->13800/tcp
xiandao-web       catalina.sh run                Up      0.0.0.0:8080->8080/tcp
```

### Step 2: Database Validation

```bash
# Connect to MySQL
docker exec -it xiandao-mysql mysql -u xiandao -pxiandao123

# Verify databases exist
SHOW DATABASES;

# Check table structure
USE xiand;
SHOW TABLES;
DESCRIBE users;

# Verify statistics database
USE xd_game_db;
SHOW TABLES;
SELECT * FROM xd_game_db;
```

**Expected Results:**
- Databases `xiand` and `xd_game_db` exist
- Tables created successfully
- Initial statistics data present

### Step 3: Pike Backend Validation

```bash
# Check Pike server logs
docker-compose logs pike-backend

# Test Pike server connectivity
telnet localhost 13800

# Verify Pike version
docker exec xiandao-pike pike8 --version
```

**Expected Results:**
- Pike server starts without errors
- MySQL connection established
- Port 13800 accepting connections
- Pike version 8.0.x displayed

### Step 4: Web Frontend Validation

```bash
# Check Tomcat logs
docker-compose logs web-frontend

# Test web interface
curl -I http://localhost:8080/xd/pc.jsp

# Verify JSP compilation
docker exec xiandao-web ls -la /usr/local/tomcat/work/Catalina/localhost/xd/
```

**Expected Results:**
- Tomcat starts successfully
- JSP files accessible
- No compilation errors in logs

### Step 5: Integration Testing

#### Test 1: Web to Pike Communication
```bash
# Access game interface
curl -s http://localhost:8080/xd/pc.jsp | grep -i "仙道"
```

#### Test 2: Database Connectivity
```bash
# Check database connections from Pike
docker exec xiandao-pike mysqladmin ping -h mysql -u xiandao -pxiandao123
```

#### Test 3: Game Functionality
1. Open browser: http://localhost:8080/xd/pc.jsp
2. Verify game interface loads
3. Test user registration (if available)
4. Check game responses

## Troubleshooting Guide

### Common Issues and Solutions

#### Pike Server Won't Start
**Symptoms:** Pike container exits immediately
**Solutions:**
1. Check MySQL readiness: `docker-compose logs mysql`
2. Verify Pike compilation: `docker-compose logs pike-backend`
3. Check port conflicts: `netstat -tulpn | grep 13800`

#### Database Connection Failed
**Symptoms:** "Can't connect to MySQL server"
**Solutions:**
1. Verify MySQL container: `docker-compose ps mysql`
2. Check credentials: `docker exec -it xiandao-mysql mysql -u root -pxiandao123`
3. Restart services: `docker-compose restart`

#### Web Interface Not Loading
**Symptoms:** HTTP 404 or 500 errors
**Solutions:**
1. Check Tomcat status: `docker-compose logs web-frontend`
2. Verify JSP deployment: `docker exec xiandao-web ls /usr/local/tomcat/webapps/xd/`
3. Check Pike backend connectivity from web container

#### Port Conflicts
**Symptoms:** "Port already in use" errors
**Solutions:**
1. Check running services: `sudo lsof -i :8080,3306,13800`
2. Stop conflicting services or change ports in docker-compose.yml
3. Use different port mappings if needed

### Performance Testing

#### Load Testing
```bash
# Test concurrent connections to Pike server
for i in {1..10}; do
  (echo "test" | nc localhost 13800 &)
done

# Monitor resource usage
docker stats
```

#### Memory Usage
```bash
# Check container memory usage
docker exec xiandao-pike free -h
docker exec xiandao-web free -h
```

## Validation Checklist

### Functional Tests
- [ ] All containers start successfully
- [ ] MySQL databases initialized correctly
- [ ] Pike server accepts connections on port 13800
- [ ] Web interface accessible on port 8080
- [ ] JSP pages compile and render
- [ ] Database connections work from Pike
- [ ] Game interface displays correctly

### Security Tests
- [ ] Database credentials secure
- [ ] No unnecessary ports exposed
- [ ] Container isolation working
- [ ] File permissions correct

### Performance Tests
- [ ] Startup time acceptable (< 2 minutes)
- [ ] Memory usage reasonable (< 1GB total)
- [ ] Response times acceptable (< 1 second)
- [ ] No memory leaks detected

## Success Criteria

The setup is considered successful when:
1. All services start and remain running
2. Web interface loads at http://localhost:8080/xd/pc.jsp
3. Game title "仙道" displays correctly
4. No critical errors in any service logs
5. Database connections established
6. Pike server responds to connections

## Cleanup

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: Deletes all data)
docker-compose down -v

# Remove images
docker-compose down --rmi all
```

## Support

For issues not covered in this guide:
1. Check service logs: `docker-compose logs [service-name]`
2. Verify configuration files
3. Consult the main README-Docker.md
4. Review original project documentation
