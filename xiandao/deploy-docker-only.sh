#!/bin/bash

# Xiandao Docker Deployment Script (Docker only, no Compose)
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NETWORK_NAME="xiandao-network"
MYSQL_CONTAINER="xiandao-mysql"
PIKE_CONTAINER="xiandao-pike"
WEB_CONTAINER="xiandao-web"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        log_info "See INSTALL-DOCKER.md for installation instructions."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        log_info "Try: sudo systemctl start docker"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Cleanup existing containers
cleanup_existing() {
    log_info "Cleaning up existing containers..."
    
    # Stop and remove containers if they exist
    docker stop $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER 2>/dev/null || true
    docker rm $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER 2>/dev/null || true
    
    # Remove network if it exists
    docker network rm $NETWORK_NAME 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Create Docker network
create_network() {
    log_info "Creating Docker network..."
    docker network create $NETWORK_NAME
    log_success "Network created: $NETWORK_NAME"
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."
    
    # Build Pike backend image
    log_info "Building Pike backend image..."
    docker build -f Dockerfile.pike -t xiandao-pike:latest .
    
    # Build Tomcat web image
    log_info "Building Tomcat web image..."
    docker build -f Dockerfile.tomcat -t xiandao-web:latest .
    
    log_success "Docker images built successfully"
}

# Start MySQL container
start_mysql() {
    log_info "Starting MySQL container..."
    
    # Create volume for MySQL data
    docker volume create mysql_data 2>/dev/null || true
    
    # Start MySQL container
    docker run -d \
        --name $MYSQL_CONTAINER \
        --network $NETWORK_NAME \
        -p 3306:3306 \
        -e MYSQL_ROOT_PASSWORD=xiandao123 \
        -e MYSQL_DATABASE=xiand \
        -e MYSQL_USER=xiandao \
        -e MYSQL_PASSWORD=xiandao123 \
        -v mysql_data:/var/lib/mysql \
        -v "$PWD/sql:/docker-entrypoint-initdb.d" \
        --restart unless-stopped \
        mysql:8.0 --default-authentication-plugin=mysql_native_password
    
    log_success "MySQL container started"
}

# Wait for MySQL to be ready
wait_for_mysql() {
    log_info "Waiting for MySQL to be ready..."
    
    local mysql_ready=false
    for i in {1..30}; do
        if docker exec $MYSQL_CONTAINER mysqladmin ping -h localhost -u root -pxiandao123 --silent 2>/dev/null; then
            mysql_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$mysql_ready" = false ]; then
        log_error "MySQL failed to start within 60 seconds"
        return 1
    fi
    log_success "MySQL is ready"
}

# Start Pike backend container
start_pike() {
    log_info "Starting Pike backend container..."
    
    # Create directories for volumes
    mkdir -p "$PWD/data_xiand" "$PWD/log"
    
    # Start Pike container
    docker run -d \
        --name $PIKE_CONTAINER \
        --network $NETWORK_NAME \
        -p 13800:13800 \
        -v "$PWD/data_xiand:/usr/local/games/xiand/data_xiand" \
        -v "$PWD/log:/usr/local/games/xiand/log" \
        -e MYSQL_HOST=mysql \
        -e MYSQL_USER=xiandao \
        -e MYSQL_PASSWORD=xiandao123 \
        -e MYSQL_DATABASE=xiand \
        --restart unless-stopped \
        xiandao-pike:latest
    
    log_success "Pike backend container started"
}

# Wait for Pike to be ready
wait_for_pike() {
    log_info "Waiting for Pike backend to be ready..."
    
    local pike_ready=false
    for i in {1..30}; do
        if nc -z localhost 13800 2>/dev/null; then
            pike_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$pike_ready" = false ]; then
        log_error "Pike backend failed to start within 60 seconds"
        return 1
    fi
    log_success "Pike backend is ready"
}

# Start web frontend container
start_web() {
    log_info "Starting web frontend container..."
    
    # Start web container
    docker run -d \
        --name $WEB_CONTAINER \
        --network $NETWORK_NAME \
        -p 8080:8080 \
        --restart unless-stopped \
        xiandao-web:latest
    
    log_success "Web frontend container started"
}

# Wait for web frontend to be ready
wait_for_web() {
    log_info "Waiting for web frontend to be ready..."
    
    local web_ready=false
    for i in {1..30}; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/xd/pc.jsp | grep -q "200\|302\|404"; then
            web_ready=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo
    
    if [ "$web_ready" = false ]; then
        log_error "Web frontend failed to start within 60 seconds"
        return 1
    fi
    log_success "Web frontend is ready"
}

# Show deployment status
show_status() {
    echo
    echo "=========================================="
    echo "Xiandao Deployment Complete!"
    echo "=========================================="
    echo
    echo "Container Status:"
    docker ps --filter "name=xiandao-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo
    echo "Access Points:"
    echo "  Web Interface: http://localhost:8080/xd/pc.jsp"
    echo "  Pike Backend:  localhost:13800"
    echo "  MySQL:         localhost:3306"
    echo
    echo "Management Commands:"
    echo "  View logs:      docker logs [container-name]"
    echo "  Stop all:       docker stop $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER"
    echo "  Start all:      docker start $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER"
    echo "  Remove all:     docker rm $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER"
    echo
    echo "Container Names:"
    echo "  MySQL:     $MYSQL_CONTAINER"
    echo "  Pike:      $PIKE_CONTAINER"
    echo "  Web:       $WEB_CONTAINER"
    echo "=========================================="
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "Deployment failed. Cleaning up..."
        docker stop $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER 2>/dev/null || true
        docker rm $MYSQL_CONTAINER $PIKE_CONTAINER $WEB_CONTAINER 2>/dev/null || true
        docker network rm $NETWORK_NAME 2>/dev/null || true
    fi
}

# Main deployment process
main() {
    trap cleanup EXIT
    
    echo "=========================================="
    echo "Xiandao Docker Deployment (Docker Only)"
    echo "=========================================="
    
    check_prerequisites
    cleanup_existing
    create_network
    build_images
    start_mysql
    wait_for_mysql
    start_pike
    wait_for_pike
    start_web
    wait_for_web
    show_status
    
    log_success "Deployment completed successfully!"
}

# Run main function
main "$@"
