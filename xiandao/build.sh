#!/bin/bash

# Xiandao Docker Build Script
set -e

echo "=========================================="
echo "Xiandao (仙道) Docker Setup"
echo "=========================================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Error: Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "Creating necessary directories..."
mkdir -p log
mkdir -p data_xiand
chmod 755 startup.sh

# Check if ports are available
echo "Checking port availability..."
if lsof -Pi :3306 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "Warning: Port 3306 is already in use. MySQL might conflict."
fi

if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "Warning: Port 8080 is already in use. Tomcat might conflict."
fi

if lsof -Pi :13800 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "Warning: Port 13800 is already in use. Pike backend might conflict."
fi

# Build and start services
echo "Building Docker images..."
docker-compose build

echo "Starting services..."
docker-compose up -d

echo "Waiting for services to start..."
sleep 10

# Check service status
echo "Checking service status..."
docker-compose ps

echo ""
echo "=========================================="
echo "Setup Complete!"
echo "=========================================="
echo "Web Interface: http://localhost:8080/xd/pc.jsp"
echo "Pike Backend:  localhost:13800"
echo "MySQL:         localhost:3306"
echo ""
echo "To view logs: docker-compose logs [service-name]"
echo "To stop:      docker-compose down"
echo "=========================================="
