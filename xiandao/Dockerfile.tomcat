# Dockerfile for Tomcat web frontend
FROM tomcat:9.0-jdk11

# Remove default webapps
RUN rm -rf /usr/local/tomcat/webapps/*

# Copy JSP frontend files
COPY frontjsp/xd /usr/local/tomcat/webapps/xd/

# Install Pike for JSP to communicate with backend
RUN apt-get update && apt-get install -y \
    build-essential \
    wget \
    curl \
    libmysqlclient-dev \
    mysql-client \
    nettle-dev \
    libgmp-dev \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libffi-dev \
    liblzma-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Download and compile Pike 8.0 for JSP communication
WORKDIR /tmp
RUN wget http://pike.lysator.liu.se/pub/pike/all/8.0.1738/Pike-v8.0.1738.tar.gz \
    && tar -xzf Pike-v8.0.1738.tar.gz \
    && cd Pike-v8.0.1738 \
    && make configure \
    && ./configure --prefix=/usr/local --with-mysql \
    && make \
    && make install \
    && ln -sf /usr/local/bin/pike /usr/local/bin/pike8

# Update configuration for Docker environment
WORKDIR /usr/local/tomcat/webapps/xd
RUN sed -i 's/127\.0\.0\.1/pike-backend/g' includes/config.inc

# Expose Tomcat port
EXPOSE 8080

# Start Tomcat
CMD ["catalina.sh", "run"]
