#!/bin/bash

# Xiandao Docker Deployment and Validation Script
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Check port availability
check_ports() {
    log_info "Checking port availability..."
    
    local ports=(3306 8080 13800)
    local conflicts=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            conflicts+=($port)
        fi
    done
    
    if [ ${#conflicts[@]} -gt 0 ]; then
        log_warning "The following ports are already in use: ${conflicts[*]}"
        log_warning "This may cause conflicts. Consider stopping services using these ports."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "All required ports are available"
    fi
}

# Prepare environment
prepare_environment() {
    log_info "Preparing environment..."
    
    # Create necessary directories
    mkdir -p log data_xiand
    
    # Set permissions
    chmod +x startup.sh build.sh
    
    log_success "Environment prepared"
}

# Build and start services
deploy_services() {
    log_info "Building and starting services..."
    
    # Build images
    log_info "Building Docker images (this may take several minutes)..."
    docker-compose build --no-cache
    
    # Start services
    log_info "Starting services..."
    docker-compose up -d
    
    log_success "Services started"
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for MySQL
    log_info "Waiting for MySQL to be ready..."
    local mysql_ready=false
    for i in {1..30}; do
        if docker exec xiandao-mysql mysqladmin ping -h localhost -u root -pxiandao123 --silent 2>/dev/null; then
            mysql_ready=true
            break
        fi
        sleep 2
    done
    
    if [ "$mysql_ready" = false ]; then
        log_error "MySQL failed to start within 60 seconds"
        return 1
    fi
    log_success "MySQL is ready"
    
    # Wait for Pike backend
    log_info "Waiting for Pike backend to be ready..."
    local pike_ready=false
    for i in {1..30}; do
        if nc -z localhost 13800 2>/dev/null; then
            pike_ready=true
            break
        fi
        sleep 2
    done
    
    if [ "$pike_ready" = false ]; then
        log_error "Pike backend failed to start within 60 seconds"
        return 1
    fi
    log_success "Pike backend is ready"
    
    # Wait for web frontend
    log_info "Waiting for web frontend to be ready..."
    local web_ready=false
    for i in {1..30}; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/xd/pc.jsp | grep -q "200\|302\|404"; then
            web_ready=true
            break
        fi
        sleep 2
    done
    
    if [ "$web_ready" = false ]; then
        log_error "Web frontend failed to start within 60 seconds"
        return 1
    fi
    log_success "Web frontend is ready"
}

# Validate deployment
validate_deployment() {
    log_info "Validating deployment..."
    
    # Check container status
    local containers=$(docker-compose ps -q)
    local running_containers=$(docker-compose ps -q --filter "status=running")
    
    if [ "$(echo "$containers" | wc -l)" != "$(echo "$running_containers" | wc -l)" ]; then
        log_error "Not all containers are running"
        docker-compose ps
        return 1
    fi
    
    # Test database connectivity
    if ! docker exec xiandao-mysql mysql -u xiandao -pxiandao123 -e "SELECT 1;" >/dev/null 2>&1; then
        log_error "Database connectivity test failed"
        return 1
    fi
    
    # Test web interface
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/xd/pc.jsp)
    if [[ ! "$http_code" =~ ^(200|302)$ ]]; then
        log_error "Web interface test failed (HTTP $http_code)"
        return 1
    fi
    
    log_success "Deployment validation passed"
}

# Show deployment status
show_status() {
    echo
    echo "=========================================="
    echo "Xiandao Deployment Complete!"
    echo "=========================================="
    echo
    echo "Services Status:"
    docker-compose ps
    echo
    echo "Access Points:"
    echo "  Web Interface: http://localhost:8080/xd/pc.jsp"
    echo "  Pike Backend:  localhost:13800"
    echo "  MySQL:         localhost:3306"
    echo
    echo "Credentials:"
    echo "  MySQL User:     xiandao"
    echo "  MySQL Password: xiandao123"
    echo "  MySQL Root:     xiandao123"
    echo
    echo "Management Commands:"
    echo "  View logs:      docker-compose logs [service-name]"
    echo "  Stop services:  docker-compose down"
    echo "  Restart:        docker-compose restart"
    echo
    echo "For detailed testing, see TESTING.md"
    echo "=========================================="
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "Deployment failed. Cleaning up..."
        docker-compose down 2>/dev/null || true
    fi
}

# Main deployment process
main() {
    trap cleanup EXIT
    
    echo "=========================================="
    echo "Xiandao Docker Deployment Script"
    echo "=========================================="
    
    check_prerequisites
    check_ports
    prepare_environment
    deploy_services
    wait_for_services
    validate_deployment
    show_status
    
    log_success "Deployment completed successfully!"
}

# Run main function
main "$@"
