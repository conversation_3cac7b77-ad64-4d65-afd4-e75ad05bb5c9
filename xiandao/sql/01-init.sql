-- Initialize Xiandao database
-- Create main game database
CREATE DATABASE IF NOT EXISTS xiand CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS xd_game_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the main database
USE xiand;

-- Create basic user table for game accounts
CREATE TABLE IF NOT EXISTS users (
    SQLKEY VARCHAR(255) PRIMARY KEY,
    SQLDATA LONGTEXT,
    NAME VARCHAR(100),
    PASSWORD VARCHAR(255),
    EMAIL VARCHAR(255),
    LEVEL INT DEFAULT 1,
    EXP BIGINT DEFAULT 0,
    GOLD BIGINT DEFAULT 0,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (NAME),
    INDEX idx_level (LEVEL)
);

-- Create items table for game items
CREATE TABLE IF NOT EXISTS items (
    S<PERSON>KEY VARCHAR(255) PRIMARY KEY,
    SQLDATA LONGTEXT,
    ITEM_ID VARCHAR(100),
    ITEM_NAME VARCHAR(255),
    ITEM_TYPE VARCHAR(50),
    OWNER VARCHAR(100),
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_owner (OWNER),
    INDEX idx_item_type (ITEM_TYPE)
);

-- Create rooms table for game world
CREATE TABLE IF NOT EXISTS rooms (
    SQLKEY VARCHAR(255) PRIMARY KEY,
    SQLDATA LONGTEXT,
    ROOM_ID VARCHAR(100),
    ROOM_NAME VARCHAR(255),
    AREA VARCHAR(100),
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_area (AREA)
);

-- Create guilds table (bangpai)
CREATE TABLE IF NOT EXISTS guilds (
    SQLKEY VARCHAR(255) PRIMARY KEY,
    SQLDATA LONGTEXT,
    GUILD_ID VARCHAR(100),
    GUILD_NAME VARCHAR(255),
    LEADER VARCHAR(100),
    MEMBER_COUNT INT DEFAULT 0,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_leader (LEADER)
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    SQLKEY VARCHAR(255) PRIMARY KEY,
    SQLDATA LONGTEXT,
    FROM_USER VARCHAR(100),
    TO_USER VARCHAR(100),
    MESSAGE TEXT,
    CREATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_to_user (TO_USER),
    INDEX idx_from_user (FROM_USER)
);

-- Use the statistics database
USE xd_game_db;

-- Create statistics table
CREATE TABLE IF NOT EXISTS xd_game_db (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stat_type VARCHAR(100),
    stat_key VARCHAR(255),
    stat_value BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_stat_type (stat_type),
    INDEX idx_stat_key (stat_key)
);

-- Insert some initial statistics
INSERT INTO xd_game_db (stat_type, stat_key, stat_value) VALUES
('player_count', 'total', 0),
('player_count', 'online', 0),
('server_status', 'uptime', 0);

-- Grant permissions to xiandao user
GRANT ALL PRIVILEGES ON xiand.* TO 'xiandao'@'%';
GRANT ALL PRIVILEGES ON xd_game_db.* TO 'xiandao'@'%';
FLUSH PRIVILEGES;
