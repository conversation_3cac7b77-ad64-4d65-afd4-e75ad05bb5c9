# Xiandao (仙道) - Docker Setup

This is a containerized version of the classic WAP-based mobile MMORPG engine "Xiandao (仙道)" originally developed in 2004-2008.

## Architecture

The system consists of three main components:

1. **Pike Backend Server** - The game engine written in Pike language
2. **MySQL Database** - Stores game data and user information
3. **Tomcat Web Frontend** - JSP-based web interface for players

## Prerequisites

- Docker
- Docker Compose
- At least 2GB of available RAM
- Ports 3306, 8080, and 13800 available

## Quick Start

1. **<PERSON>lone and navigate to the project directory:**
   ```bash
   cd xiandao
   ```

2. **Build and start all services:**
   ```bash
   docker-compose up --build
   ```

3. **Access the game:**
   - Web Interface: http://localhost:8080/xd/pc.jsp
   - Pike Backend: localhost:13800
   - MySQL Database: localhost:3306

## Services

### MySQL Database
- **Container:** xiandao-mysql
- **Port:** 3306
- **Databases:** xiand, xd_game_db
- **User:** xiandao / xiandao123
- **Root Password:** xiandao123

### Pike Backend
- **Container:** xiandao-pike
- **Port:** 13800
- **Language:** Pike 8.0
- **Game Logic:** /usr/local/games/xiand/

### Web Frontend
- **Container:** xiandao-web
- **Port:** 8080
- **Technology:** Tomcat 9 + JSP
- **Entry Point:** /xd/pc.jsp

## Development

### Logs
View logs for each service:
```bash
# Pike backend logs
docker-compose logs pike-backend

# Web frontend logs
docker-compose logs web-frontend

# Database logs
docker-compose logs mysql
```

### Database Access
Connect to MySQL:
```bash
docker exec -it xiandao-mysql mysql -u xiandao -pxiandao123 xiand
```

### File Persistence
- Game data: `./data_xiand/` (mounted to Pike container)
- Logs: `./log/` (mounted to Pike container)
- Database: Docker volume `mysql_data`

## Troubleshooting

### Pike Server Won't Start
1. Check if MySQL is ready:
   ```bash
   docker-compose logs mysql
   ```
2. Verify Pike compilation:
   ```bash
   docker-compose exec pike-backend pike8 --version
   ```

### Web Interface Not Loading
1. Check Tomcat status:
   ```bash
   docker-compose logs web-frontend
   ```
2. Verify JSP files are deployed:
   ```bash
   docker-compose exec web-frontend ls -la /usr/local/tomcat/webapps/xd/
   ```

### Database Connection Issues
1. Test MySQL connectivity:
   ```bash
   docker-compose exec pike-backend mysqladmin ping -h mysql -u xiandao -pxiandao123
   ```

## Customization

### Game Configuration
Edit `lowlib/system/include/globals.h` for game settings.

### Web Configuration
Edit `frontjsp/xd/includes/config.inc` for web interface settings.

### Database Schema
Modify `sql/01-init.sql` for database structure changes.

## Stopping the Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete all game data)
docker-compose down -v
```

## Original Project

This is based on the open-source Xiandao project:
- GitHub: https://github.com/lijingmt/xd
- License: MIT
- Original Author: Jing Li and team

## Support

For issues related to the Docker setup, please check the logs and troubleshooting section above.
For game-specific issues, refer to the original project documentation.
