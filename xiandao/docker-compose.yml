version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: xiandao-mysql
    environment:
      MYSQL_ROOT_PASSWORD: xiandao123
      MYSQL_DATABASE: xiand
      MYSQL_USER: xiandao
      MYSQL_PASSWORD: xiandao123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    networks:
      - xiandao-network
    command: --default-authentication-plugin=mysql_native_password

  # Pike Backend Server
  pike-backend:
    build:
      context: .
      dockerfile: Dockerfile.pike
    container_name: xiandao-pike
    ports:
      - "13800:13800"
    depends_on:
      - mysql
    volumes:
      - ./data_xiand:/usr/local/games/xiand/data_xiand
      - ./log:/usr/local/games/xiand/log
    networks:
      - xiandao-network
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_USER=xiandao
      - MYSQL_PASSWORD=xiandao123
      - MYSQL_DATABASE=xiand
    restart: unless-stopped

  # Tomcat Web Frontend
  web-frontend:
    build:
      context: .
      dockerfile: Dockerfile.tomcat
    container_name: xiandao-web
    ports:
      - "8080:8080"
    depends_on:
      - pike-backend
    networks:
      - xiandao-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  xiandao-network:
    driver: bridge
