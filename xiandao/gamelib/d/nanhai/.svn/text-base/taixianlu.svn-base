#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="third";
static int room_level=705;
void create(){
	name=object_name(this_object());
	name_cn="̦޺·";
	desc="\n";
	exits["east"]=ROOT "/gamelib/d/nanhai/simingxie";
	exits["west"]=ROOT "/gamelib/d/nanhai/haiminghu";
	add_items(({ROOT "/gamelib/clone/npc/waihai/sandaohaiguijing43"}));
	add_items(({ROOT "/gamelib/clone/npc/waihai/kuguluo43"}));
	add_items(({ROOT "/gamelib/clone/npc/waihai/chijirenyu43"}));
	add_items(({ROOT "/gamelib/clone/npc/waihai/qingyanxieyao43"}));
	add_items(({ROOT "/gamelib/clone/npc/waihai/hongshujing43"}));
	add_items(({ROOT "/gamelib/clone/npc/waihai/heibeihaima43"}));
}
