#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="monst";
static int room_level=10000;
void create(){
	name=object_name(this_object());
	name_cn="ǳ̲";
	desc="\n";
	exits["east"]=ROOT "/gamelib/d/jadhuanjingwaicheng/guanmulin";
	exits["west"]=ROOT "/gamelib/d/jadhuanjingwaicheng/chaoxidao";
	add_items(({ROOT "/gamelib/clone/npc/jadhuanjingwaicheng/yinjiaomilu70"}));
	add_items(({ROOT "/gamelib/clone/npc/jadhuanjingwaicheng/xihuoyeniu70"}));
	add_items(({ROOT "/gamelib/clone/npc/jadhuanjingwaicheng/jinxianfushe70"}));
	add_items(({ROOT "/gamelib/clone/npc/jadhuanjingwaicheng/jushilonggui70"}));
	add_items(({ROOT "/gamelib/clone/npc/jadhuanjingwaicheng/muwenshujing70"}));
}
