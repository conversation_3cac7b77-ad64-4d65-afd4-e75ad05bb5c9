#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="third";
static int room_level=307;
void create(){
	name=object_name(this_object());
	name_cn="ůˮ̲";
	desc="\n";
	exits["north"]=ROOT "/gamelib/d/liangjinghu/wenshuichi";
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/shuixichong25"}));
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/xiaoheyao25"}));
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/shiroufuping25"}));
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/hehuanv25"}));
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/baiyanlianpeng25"}));
	add_items(({ROOT "/gamelib/clone/npc/liangjinghu/duouguai25"}));
}
