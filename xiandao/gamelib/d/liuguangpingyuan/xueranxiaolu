#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="third";
static int room_level=1193;
void create(){
	name=object_name(this_object());
	name_cn="ѩȻС·";
	desc="\n";
	exits["south"]=ROOT "/gamelib/d/liuguangpingyuan/liuguangchalu";
	exits["north"]=ROOT "/gamelib/d/liuguangpingyuan/xuerancun";
	add_items(({ROOT "/gamelib/clone/npc/liuguangpingyuan/50pingyuanxunluobing"}));
	add_items(({ROOT "/gamelib/clone/npc/liuguangpingyuan/50pingyuanheibao"}));
	add_items(({ROOT "/gamelib/clone/npc/liuguangpingyuan/50liuguangjushe"}));
	add_items(({ROOT "/gamelib/clone/npc/liuguangpingyuan/50liuguangduxie"}));
	add_items(({ROOT "/gamelib/clone/npc/liuguangpingyuan/50pingyuanxuelang"}));
}
