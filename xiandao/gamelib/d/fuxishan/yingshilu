#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="monst";
static int room_level=775;
void create(){
	name=object_name(this_object());
	name_cn="Ӳʯ·";
	desc="\n";
	exits["east"]=ROOT "/gamelib/d/fuxishan/fuxidongbushaoka";
	exits["west"]=ROOT "/gamelib/d/fuxishan/banshanchalu";
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50fuxijingbing"}));
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50shenshankuxiuzhe"}));
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50fuxishanlinshouwei"}));
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50fuxibianyishaobing"}));
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50shenshanqianfuzhe"}));
	add_items(({ROOT "/gamelib/clone/npc/fuxishan/50shenshancangnizhe"}));
}
