#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="human";
static int room_level=43;
void create(){
	name=object_name(this_object());
	name_cn="çԭ";
	desc="\n";
	exits["east"]=ROOT "/gamelib/d/kunlunshan/piaohuaxi";
	exits["west"]=ROOT "/gamelib/d/kunlunshan/huaxuepingyuan";
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/qinyuan6"}));
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/shuanglang6"}));
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/yaodao6"}));
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/tulou6"}));
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/wunaojiangshi6"}));
	add_items(({ROOT "/gamelib/clone/npc/kunlunshan/shiroubianfu6"}));
}
