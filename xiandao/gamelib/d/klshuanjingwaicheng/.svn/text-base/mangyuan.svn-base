#include <globals.h>
#include <gamelib/include/gamelib.h>
inherit WAP_ROOM;
string room_race="human";
static int room_level=10000;
void create(){
	name=object_name(this_object());
	name_cn="çԭ";
	desc="\n";
	exits["east"]=ROOT "/gamelib/d/klshuanjingwaicheng/wuyaoying4";
	exits["west"]=ROOT "/gamelib/d/klshuanjingwaicheng/huaxuepingyuan";
	add_items(({ROOT "/gamelib/clone/npc/klshuanjingwaicheng/wutoufuhun70"}));
	add_items(({ROOT "/gamelib/clone/npc/klshuanjingwaicheng/sishoushanxiao70"}));
	add_items(({ROOT "/gamelib/clone/npc/klshuanjingwaicheng/chuanxingui70"}));
	add_items(({ROOT "/gamelib/clone/npc/klshuanjingwaicheng/yingyinkuilei70"}));
	add_items(({ROOT "/gamelib/clone/npc/klshuanjingwaicheng/jianjiaoliedichong70"}));
}
